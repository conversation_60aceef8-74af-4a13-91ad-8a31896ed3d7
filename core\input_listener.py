import threading
import time
from pynput import mouse, keyboard
from pynput.mouse import <PERSON><PERSON>, Listener as MouseListener
from pynput.keyboard import Key, Listener as KeyboardListener
from typing import Callable, Optional
from utils.safety import SafetyManager

class InputListener:
    """输入监听器，监听鼠标点击和键盘热键"""
    
    def __init__(self, safety_manager: SafetyManager):
        self.safety_manager = safety_manager
        self.mouse_listener: Optional[MouseListener] = None
        self.keyboard_listener: Optional[KeyboardListener] = None
        
        # 回调函数
        self.on_fire_start: Optional[Callable] = None
        self.on_fire_stop: Optional[Callable] = None
        self.on_toggle_activation: Optional[Callable] = None
        self.on_emergency_stop: Optional[Callable] = None
        self.on_weapon_switch: Optional[Callable] = None
        
        # 状态跟踪
        self.is_firing = False
        self.is_listening = False
        self.left_button_pressed = False
        
        # 热键配置
        self.toggle_key = Key.f1  # F1切换启用/禁用
        self.emergency_key = Key.f2  # F2紧急停止
        self.weapon_switch_keys = {
            Key.f3: "ak47",    # F3切换到AK47
            Key.f4: "m4a1",    # F4切换到M4A1
            Key.f5: "scar"     # F5切换到SCAR
        }
        
        # 按键状态
        self.pressed_keys = set()
        
    def start_listening(self):
        """开始监听输入"""
        if self.is_listening:
            return
        
        self.is_listening = True
        
        # 启动鼠标监听器
        self.mouse_listener = MouseListener(
            on_click=self._on_mouse_click,
            suppress=False
        )
        
        # 启动键盘监听器
        self.keyboard_listener = KeyboardListener(
            on_press=self._on_key_press,
            on_release=self._on_key_release,
            suppress=False
        )
        
        self.mouse_listener.start()
        self.keyboard_listener.start()
        
        print("输入监听器已启动")
        print("热键说明:")
        print("  F1 - 切换压枪启用/禁用")
        print("  F2 - 紧急停止")
        print("  F3 - 切换到AK47")
        print("  F4 - 切换到M4A1")
        print("  F5 - 切换到SCAR")
        print("  鼠标左键 - 开火触发压枪")
    
    def stop_listening(self):
        """停止监听输入"""
        if not self.is_listening:
            return
        
        self.is_listening = False
        
        if self.mouse_listener:
            self.mouse_listener.stop()
            self.mouse_listener = None
        
        if self.keyboard_listener:
            self.keyboard_listener.stop()
            self.keyboard_listener = None
        
        print("输入监听器已停止")
    
    def _on_mouse_click(self, x, y, button, pressed):
        """鼠标点击事件处理"""
        if button == Button.left:
            if pressed and not self.left_button_pressed:
                # 鼠标左键按下
                self.left_button_pressed = True
                self._handle_fire_start()
            elif not pressed and self.left_button_pressed:
                # 鼠标左键释放
                self.left_button_pressed = False
                self._handle_fire_stop()
    
    def _on_key_press(self, key):
        """键盘按键按下事件"""
        if key in self.pressed_keys:
            return  # 避免重复处理
        
        self.pressed_keys.add(key)
        
        try:
            # 处理热键
            if key == self.toggle_key:
                self._handle_toggle_activation()
            elif key == self.emergency_key:
                self._handle_emergency_stop()
            elif key in self.weapon_switch_keys:
                weapon_name = self.weapon_switch_keys[key]
                self._handle_weapon_switch(weapon_name)
                
        except Exception as e:
            print(f"处理按键事件时出错: {e}")
    
    def _on_key_release(self, key):
        """键盘按键释放事件"""
        self.pressed_keys.discard(key)
    
    def _handle_fire_start(self):
        """处理开火开始"""
        if not self.safety_manager.is_safe_to_operate():
            return
        
        if not self.is_firing:
            self.is_firing = True
            if self.on_fire_start:
                try:
                    self.on_fire_start()
                except Exception as e:
                    print(f"执行开火开始回调时出错: {e}")
    
    def _handle_fire_stop(self):
        """处理开火停止"""
        if self.is_firing:
            self.is_firing = False
            if self.on_fire_stop:
                try:
                    self.on_fire_stop()
                except Exception as e:
                    print(f"执行开火停止回调时出错: {e}")
    
    def _handle_toggle_activation(self):
        """处理切换激活状态"""
        if self.on_toggle_activation:
            try:
                self.on_toggle_activation()
            except Exception as e:
                print(f"执行切换激活回调时出错: {e}")
    
    def _handle_emergency_stop(self):
        """处理紧急停止"""
        print("🚨 紧急停止热键触发")
        self.safety_manager.emergency_shutdown()
        
        if self.on_emergency_stop:
            try:
                self.on_emergency_stop()
            except Exception as e:
                print(f"执行紧急停止回调时出错: {e}")
    
    def _handle_weapon_switch(self, weapon_name: str):
        """处理武器切换"""
        if self.on_weapon_switch:
            try:
                self.on_weapon_switch(weapon_name)
            except Exception as e:
                print(f"执行武器切换回调时出错: {e}")
    
    def set_fire_callbacks(self, on_start: Callable, on_stop: Callable):
        """设置开火回调函数"""
        self.on_fire_start = on_start
        self.on_fire_stop = on_stop
    
    def set_control_callbacks(self, on_toggle: Callable, on_emergency: Callable):
        """设置控制回调函数"""
        self.on_toggle_activation = on_toggle
        self.on_emergency_stop = on_emergency
    
    def set_weapon_callback(self, on_weapon_switch: Callable):
        """设置武器切换回调函数"""
        self.on_weapon_switch = on_weapon_switch
    
    def update_hotkeys(self, toggle_key=None, emergency_key=None, weapon_keys=None):
        """更新热键配置"""
        if toggle_key:
            self.toggle_key = toggle_key
        if emergency_key:
            self.emergency_key = emergency_key
        if weapon_keys:
            self.weapon_switch_keys.update(weapon_keys)
        
        print("热键配置已更新")
    
    def get_status(self) -> dict:
        """获取监听器状态"""
        return {
            "is_listening": self.is_listening,
            "is_firing": self.is_firing,
            "left_button_pressed": self.left_button_pressed,
            "pressed_keys_count": len(self.pressed_keys)
        }
