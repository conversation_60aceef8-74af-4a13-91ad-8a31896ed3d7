# 三角洲压枪小工具 v1.0.0

一个用于三角洲游戏的自动压枪辅助工具，支持多种武器配置和智能安全控制。

## ⚠️ 重要声明

**本工具仅供学习和研究使用，请遵守游戏服务条款和相关法律法规。**

- 🚫 不建议在正式游戏中使用
- 📚 仅用于学习编程和算法研究
- ⚖️ 使用者需自行承担相关责任
- 🎯 建议在训练模式下测试功能

## 🚀 快速开始

### 环境要求

- Python 3.7+
- Windows 10/11 (推荐)
- 管理员权限 (用于输入监听)

### 安装依赖

```bash
pip install -r requirements.txt
```

### 运行程序

```bash
python main.py
```

## 🎮 使用说明

### 热键控制

| 热键 | 功能 |
|------|------|
| F1 | 启用/禁用压枪功能 |
| F2 | 紧急停止 |
| F3 | 切换到 AK-47 |
| F4 | 切换到 M4A1 |
| F5 | 切换到 SCAR-L |

### 鼠标控制

- **按住鼠标左键**: 在启用状态下自动执行压枪

### 基本操作流程

1. 🚀 启动程序: `python main.py`
2. 🔧 按F1启用压枪功能
3. 🔫 使用F3-F5选择武器
4. 🎯 在游戏中按住鼠标左键开火
5. 🛑 按F2紧急停止或Ctrl+C退出

## 🔧 配置说明

### 武器配置文件

配置文件位于 `config/weapons.json`，包含以下武器:

- **AK-47**: 高后坐力突击步枪
- **M4A1**: 中等后坐力突击步枪  
- **SCAR-L**: 平衡型突击步枪

### 配置参数说明

```json
{
  "recoil_pattern": [
    {"x": 0, "y": 2, "delay": 0.1}
  ],
  "sensitivity": 1.0,
  "max_bullets": 30
}
```

- `x, y`: 鼠标移动偏移量
- `delay`: 步骤间延迟时间
- `sensitivity`: 武器灵敏度倍数
- `max_bullets`: 最大子弹数

### 全局设置

```json
{
  "mouse_sensitivity": 1.0,
  "dpi": 800,
  "smoothing": true,
  "randomization": 0.1
}
```

## 🛡️ 安全机制

### 自动安全保护

- ⏰ **运行时间限制**: 最大连续运行5分钟
- 🚨 **紧急停止**: F2键快速停止
- 🔒 **状态检查**: 实时监控运行状态
- 🎯 **鼠标检测**: 检测异常鼠标移动

### 安全使用建议

1. 🧪 **测试环境**: 先在训练模式测试
2. ⚙️ **参数调整**: 根据个人习惯调整灵敏度
3. 🔄 **定期休息**: 避免长时间连续使用
4. 📖 **了解规则**: 熟悉游戏服务条款

## 📁 项目结构

```
recoil_control/
├── main.py              # 主程序入口
├── requirements.txt     # 依赖包列表
├── README.md           # 使用说明
├── config/             # 配置文件目录
│   ├── __init__.py
│   ├── settings.py     # 配置管理
│   └── weapons.json    # 武器参数
├── core/               # 核心功能模块
│   ├── __init__.py
│   ├── controller.py   # 主控制器
│   ├── mouse_handler.py # 鼠标控制
│   └── input_listener.py # 输入监听
└── utils/              # 工具模块
    ├── __init__.py
    └── safety.py       # 安全机制
```

## 🔧 自定义配置

### 添加新武器

1. 编辑 `config/weapons.json`
2. 添加新武器配置
3. 重新启动程序或使用重载配置功能

### 修改热键

编辑 `core/input_listener.py` 中的热键配置:

```python
self.toggle_key = Key.f1
self.emergency_key = Key.f2
self.weapon_switch_keys = {
    Key.f3: "ak47",
    Key.f4: "m4a1",
    Key.f5: "scar"
}
```

## 🐛 故障排除

### 常见问题

1. **程序无法启动**
   - 检查Python版本 (需要3.7+)
   - 确认已安装所有依赖包
   - 以管理员权限运行

2. **热键无响应**
   - 确认程序正在运行
   - 检查是否有其他程序占用热键
   - 重启程序

3. **压枪效果不理想**
   - 调整武器灵敏度参数
   - 检查游戏内鼠标设置
   - 确认DPI设置正确

### 日志信息

程序运行时会在控制台显示详细的状态信息，包括:
- 组件启动状态
- 热键响应情况
- 安全检查结果
- 错误信息

## 📄 许可证

本项目仅供学习和研究使用，不得用于商业用途。

## 🤝 贡献

欢迎提交问题报告和改进建议。

---

**再次提醒: 请负责任地使用本工具，遵守游戏规则和相关法律法规。**
