import json
import os
from typing import Dict, Any, Optional

class ConfigManager:
    """配置管理器，负责加载和管理武器配置"""
    
    def __init__(self, config_path: str = "config/weapons.json"):
        self.config_path = config_path
        self.config_data = {}
        self.current_weapon = "ak47"
        self.load_config()
    
    def load_config(self) -> bool:
        """加载配置文件"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    self.config_data = json.load(f)
                print(f"配置文件加载成功: {self.config_path}")
                return True
            else:
                print(f"配置文件不存在: {self.config_path}")
                return False
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return False
    
    def get_weapon_config(self, weapon_name: str) -> Optional[Dict[str, Any]]:
        """获取指定武器的配置"""
        weapons = self.config_data.get("weapons", {})
        return weapons.get(weapon_name)
    
    def get_current_weapon_config(self) -> Optional[Dict[str, Any]]:
        """获取当前武器的配置"""
        return self.get_weapon_config(self.current_weapon)
    
    def set_current_weapon(self, weapon_name: str) -> bool:
        """设置当前武器"""
        if weapon_name in self.config_data.get("weapons", {}):
            self.current_weapon = weapon_name
            print(f"当前武器设置为: {weapon_name}")
            return True
        else:
            print(f"未找到武器配置: {weapon_name}")
            return False
    
    def get_available_weapons(self) -> list:
        """获取可用武器列表"""
        return list(self.config_data.get("weapons", {}).keys())
    
    def get_global_settings(self) -> Dict[str, Any]:
        """获取全局设置"""
        return self.config_data.get("global_settings", {})
    
    def get_recoil_pattern(self, weapon_name: str = None) -> list:
        """获取武器的后坐力模式"""
        weapon_name = weapon_name or self.current_weapon
        weapon_config = self.get_weapon_config(weapon_name)
        if weapon_config:
            return weapon_config.get("recoil_pattern", [])
        return []
    
    def get_weapon_sensitivity(self, weapon_name: str = None) -> float:
        """获取武器灵敏度"""
        weapon_name = weapon_name or self.current_weapon
        weapon_config = self.get_weapon_config(weapon_name)
        if weapon_config:
            return weapon_config.get("sensitivity", 1.0)
        return 1.0
    
    def update_global_setting(self, key: str, value: Any) -> bool:
        """更新全局设置"""
        try:
            if "global_settings" not in self.config_data:
                self.config_data["global_settings"] = {}
            
            self.config_data["global_settings"][key] = value
            self.save_config()
            return True
        except Exception as e:
            print(f"更新全局设置失败: {e}")
            return False
    
    def save_config(self) -> bool:
        """保存配置到文件"""
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self.config_data, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"保存配置文件失败: {e}")
            return False
