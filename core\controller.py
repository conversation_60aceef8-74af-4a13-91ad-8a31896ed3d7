import time
import threading
from typing import Optional
from config.settings import Config<PERSON>anager
from core.mouse_handler import <PERSON><PERSON><PERSON><PERSON>
from core.input_listener import InputListener
from utils.safety import SafetyManager

class RecoilController:
    """主控制器，协调各个组件实现压枪功能"""
    
    def __init__(self):
        # 初始化组件
        self.config_manager = ConfigManager()
        self.safety_manager = SafetyManager()
        self.mouse_handler = MouseHandler(self.safety_manager)
        self.input_listener = InputListener(self.safety_manager)
        
        # 状态管理
        self.is_enabled = False
        self.is_running = False
        
        # 设置回调函数
        self._setup_callbacks()
        
        # 应用全局设置
        self._apply_global_settings()
        
        print("压枪控制器初始化完成")
    
    def _setup_callbacks(self):
        """设置各组件的回调函数"""
        # 设置输入监听器回调
        self.input_listener.set_fire_callbacks(
            on_start=self._on_fire_start,
            on_stop=self._on_fire_stop
        )
        
        self.input_listener.set_control_callbacks(
            on_toggle=self._on_toggle_activation,
            on_emergency=self._on_emergency_stop
        )
        
        self.input_listener.set_weapon_callback(self._on_weapon_switch)
        
        # 设置安全管理器回调
        self.safety_manager.set_stop_callback(self._emergency_shutdown)
    
    def _apply_global_settings(self):
        """应用全局设置"""
        global_settings = self.config_manager.get_global_settings()
        
        # 应用鼠标设置
        mouse_sensitivity = global_settings.get("mouse_sensitivity", 1.0)
        self.mouse_handler.set_sensitivity(mouse_sensitivity)
        
        smoothing = global_settings.get("smoothing", True)
        self.mouse_handler.set_smoothing(smoothing)
        
        randomization = global_settings.get("randomization", 0.1)
        self.mouse_handler.set_randomization(randomization)
    
    def start(self):
        """启动压枪控制器"""
        if self.is_running:
            print("控制器已在运行中")
            return False
        
        try:
            # 启动输入监听
            self.input_listener.start_listening()
            
            # 激活安全管理器
            if not self.safety_manager.activate():
                self.input_listener.stop_listening()
                return False
            
            self.is_running = True
            print("✅ 压枪控制器已启动")
            print(f"当前武器: {self.config_manager.current_weapon}")
            print("按F1启用/禁用压枪功能")
            
            return True
            
        except Exception as e:
            print(f"启动控制器失败: {e}")
            self.stop()
            return False
    
    def stop(self):
        """停止压枪控制器"""
        if not self.is_running:
            return
        
        print("正在停止压枪控制器...")
        
        # 停止压枪控制
        self.mouse_handler.stop_recoil_control()
        
        # 停用安全管理器
        self.safety_manager.deactivate()
        
        # 停止输入监听
        self.input_listener.stop_listening()
        
        self.is_enabled = False
        self.is_running = False
        
        print("✅ 压枪控制器已停止")
    
    def _on_fire_start(self):
        """开火开始回调"""
        if not self.is_enabled or not self.safety_manager.is_safe_to_operate():
            return
        
        # 获取当前武器配置
        weapon_config = self.config_manager.get_current_weapon_config()
        if not weapon_config:
            print("未找到当前武器配置")
            return
        
        recoil_pattern = weapon_config.get("recoil_pattern", [])
        weapon_sensitivity = weapon_config.get("sensitivity", 1.0)
        
        if recoil_pattern:
            print(f"开始压枪: {weapon_config.get('name', '未知武器')}")
            self.mouse_handler.start_recoil_control(recoil_pattern, weapon_sensitivity)
        else:
            print("当前武器没有配置压枪模式")
    
    def _on_fire_stop(self):
        """开火停止回调"""
        if self.mouse_handler.is_active():
            print("停止压枪")
            self.mouse_handler.stop_recoil_control()
    
    def _on_toggle_activation(self):
        """切换激活状态回调"""
        if not self.is_running:
            return
        
        self.is_enabled = not self.is_enabled
        status = "启用" if self.is_enabled else "禁用"
        print(f"🔄 压枪功能已{status}")
        
        # 如果禁用，停止当前的压枪控制
        if not self.is_enabled:
            self.mouse_handler.stop_recoil_control()
    
    def _on_emergency_stop(self):
        """紧急停止回调"""
        print("🚨 执行紧急停止")
        self.mouse_handler.stop_recoil_control()
        self.is_enabled = False
    
    def _on_weapon_switch(self, weapon_name: str):
        """武器切换回调"""
        if self.config_manager.set_current_weapon(weapon_name):
            weapon_config = self.config_manager.get_current_weapon_config()
            weapon_display_name = weapon_config.get("name", weapon_name) if weapon_config else weapon_name
            print(f"🔫 切换武器: {weapon_display_name}")
            
            # 如果正在压枪，停止当前压枪
            if self.mouse_handler.is_active():
                self.mouse_handler.stop_recoil_control()
        else:
            print(f"❌ 武器切换失败: {weapon_name}")
    
    def _emergency_shutdown(self):
        """紧急关闭回调"""
        print("🚨 安全管理器触发紧急关闭")
        self.mouse_handler.stop_recoil_control()
        self.is_enabled = False
    
    def get_status(self) -> dict:
        """获取控制器状态"""
        return {
            "is_running": self.is_running,
            "is_enabled": self.is_enabled,
            "current_weapon": self.config_manager.current_weapon,
            "available_weapons": self.config_manager.get_available_weapons(),
            "mouse_active": self.mouse_handler.is_active(),
            "safety_status": self.safety_manager.get_status(),
            "input_status": self.input_listener.get_status()
        }
    
    def set_weapon(self, weapon_name: str) -> bool:
        """手动设置武器"""
        return self.config_manager.set_current_weapon(weapon_name)
    
    def reload_config(self) -> bool:
        """重新加载配置"""
        if self.config_manager.load_config():
            self._apply_global_settings()
            print("配置已重新加载")
            return True
        return False
