#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
三角洲压枪小工具
自动压枪辅助程序

作者: AI Assistant
版本: 1.0.0
"""

import sys
import time
import signal
from core.controller import RecoilController

class RecoilControlApp:
    """压枪控制应用程序"""
    
    def __init__(self):
        self.controller = RecoilController()
        self.running = False
        
        # 设置信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"\n收到信号 {signum}，正在安全退出...")
        self.stop()
        sys.exit(0)
    
    def print_banner(self):
        """打印程序横幅"""
        banner = """
╔══════════════════════════════════════════════════════════════╗
║                    三角洲压枪小工具 v1.0.0                    ║
║                                                              ║
║  🎯 自动压枪辅助程序                                          ║
║  ⚠️  仅供学习和研究使用，请遵守游戏规则                        ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
        """
        print(banner)
    
    def print_instructions(self):
        """打印使用说明"""
        instructions = """
📋 使用说明:
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

🎮 热键控制:
  F1  - 启用/禁用压枪功能
  F2  - 紧急停止
  F3  - 切换到 AK-47
  F4  - 切换到 M4A1
  F5  - 切换到 SCAR-L

🖱️  鼠标控制:
  按住鼠标左键 - 自动执行压枪

💡 使用提示:
  1. 启动程序后，按F1启用压枪功能
  2. 在游戏中按住鼠标左键开火时会自动压枪
  3. 可以使用F3-F5快速切换武器配置
  4. 遇到问题时按F2紧急停止
  5. 按Ctrl+C安全退出程序

⚠️  安全提醒:
  • 本工具仅供学习研究使用
  • 请遵守游戏服务条款
  • 建议在训练模式下测试
  • 程序具有自动安全保护机制

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
        """
        print(instructions)
    
    def print_status(self):
        """打印当前状态"""
        status = self.controller.get_status()
        
        print("\n" + "="*60)
        print("📊 当前状态:")
        print(f"  运行状态: {'🟢 运行中' if status['is_running'] else '🔴 已停止'}")
        print(f"  压枪功能: {'🟢 启用' if status['is_enabled'] else '🔴 禁用'}")
        print(f"  当前武器: 🔫 {status['current_weapon'].upper()}")
        print(f"  鼠标控制: {'🟢 活动' if status['mouse_active'] else '🔴 待机'}")
        
        safety_status = status['safety_status']
        print(f"  安全状态: {'🟢 正常' if safety_status['is_active'] and not safety_status['emergency_stop'] else '🔴 异常'}")
        
        if safety_status['is_active']:
            remaining_time = safety_status['time_remaining']
            print(f"  剩余时间: {remaining_time:.0f}秒")
        
        print("="*60)
    
    def interactive_menu(self):
        """交互式菜单"""
        while self.running:
            print("\n🎛️  控制菜单:")
            print("  1. 查看状态")
            print("  2. 切换武器")
            print("  3. 重新加载配置")
            print("  4. 查看可用武器")
            print("  0. 退出程序")
            
            try:
                choice = input("\n请选择操作 (0-4): ").strip()
                
                if choice == "1":
                    self.print_status()
                elif choice == "2":
                    self._weapon_selection_menu()
                elif choice == "3":
                    if self.controller.reload_config():
                        print("✅ 配置重新加载成功")
                    else:
                        print("❌ 配置重新加载失败")
                elif choice == "4":
                    self._show_available_weapons()
                elif choice == "0":
                    break
                else:
                    print("❌ 无效选择，请重试")
                    
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"❌ 操作出错: {e}")
    
    def _weapon_selection_menu(self):
        """武器选择菜单"""
        weapons = self.controller.get_status()['available_weapons']
        
        print("\n🔫 可用武器:")
        for i, weapon in enumerate(weapons, 1):
            print(f"  {i}. {weapon.upper()}")
        
        try:
            choice = input(f"\n选择武器 (1-{len(weapons)}): ").strip()
            index = int(choice) - 1
            
            if 0 <= index < len(weapons):
                weapon_name = weapons[index]
                if self.controller.set_weapon(weapon_name):
                    print(f"✅ 武器已切换到: {weapon_name.upper()}")
                else:
                    print(f"❌ 武器切换失败: {weapon_name}")
            else:
                print("❌ 无效选择")
                
        except (ValueError, IndexError):
            print("❌ 输入无效")
    
    def _show_available_weapons(self):
        """显示可用武器"""
        weapons = self.controller.get_status()['available_weapons']
        current_weapon = self.controller.get_status()['current_weapon']
        
        print("\n🔫 武器列表:")
        for weapon in weapons:
            marker = "👉" if weapon == current_weapon else "  "
            print(f"{marker} {weapon.upper()}")
    
    def start(self):
        """启动应用程序"""
        self.print_banner()
        self.print_instructions()
        
        print("🚀 正在启动压枪控制器...")
        
        if not self.controller.start():
            print("❌ 启动失败，程序退出")
            return False
        
        self.running = True
        self.print_status()
        
        print("\n✅ 程序已启动！")
        print("💡 提示: 按F1启用压枪功能，按Ctrl+C退出程序")
        
        try:
            # 启动交互式菜单
            self.interactive_menu()
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，正在退出...")
        except Exception as e:
            print(f"\n❌ 程序运行出错: {e}")
        finally:
            self.stop()
        
        return True
    
    def stop(self):
        """停止应用程序"""
        if self.running:
            print("🛑 正在停止程序...")
            self.running = False
            self.controller.stop()
            print("✅ 程序已安全退出")

def main():
    """主函数"""
    try:
        app = RecoilControlApp()
        app.start()
    except Exception as e:
        print(f"❌ 程序启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
